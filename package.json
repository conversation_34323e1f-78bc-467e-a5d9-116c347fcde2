{"name": "inhand-sales-assistant", "version": "0.3.0", "private": true, "description": "InHand Sales Assistant is a browser extension.", "type": "module", "scripts": {"build": "cross-env NODE_ENV=production wxt build && wxt zip && cross-env NODE_ENV=production wxt build -b safari && wxt zip -b safari", "build:firefox": "cross-env NODE_ENV=production wxt build -b firefox && wxt zip -b firefox", "build:safari": "cross-env NODE_ENV=production wxt build -b safari && wxt zip -b safari", "compile": "tsc --noEmit", "dev": "cross-env NODE_ENV=development wxt --port 8000 --mode development", "dev:firefox": "cross-env NODE_ENV=development wxt -b firefox --port 8000", "dev:safari": "cross-env NODE_ENV=development wxt -b safari --port 8000", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "postinstall": "wxt prepare", "lint": "pnpm run lint:check", "lint:check": "eslint . --ext .ts,.tsx && pnpm run stylelint:check && prettier --check \"**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "lint:fix": "eslint . --ext .ts,.tsx --fix && pnpm run stylelint:fix && pnpm run format", "prepare": "wxt prepare && husky", "start": "wxt --port 8000", "stylelint:check": "stylelint \"components/**/*.css\" \"entrypoints/**/*.css\"", "stylelint:fix": "stylelint \"components/**/*.css\" \"entrypoints/**/*.css\" --fix", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "zip:safari": "wxt zip -b safari"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "~5.6.1", "@microsoft/fetch-event-source": "^2.0.1", "@wxt-dev/storage": "^1.1.1", "@wxt-dev/webextension-polyfill": "^1.0.0", "ahooks": "^3.8.5", "antd": "^5.25.3", "antd-style": "^3.7.1", "axios": "^1.9.0", "es-toolkit": "^1.38.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-use": "^17.6.0", "webextension-polyfill": "^0.12.0"}, "devDependencies": {"@babel/eslint-parser": "^7.27.1", "@babel/eslint-plugin": "^7.27.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@types/chrome": "^0.0.317", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-react": "^4.5.0", "@wxt-dev/auto-icons": "^1.0.2", "@wxt-dev/module-react": "^1.1.3", "cross-env": "^7.0.3", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-you-might-not-need-an-effect": "^0.0.28", "eslint-plugin-unicorn": "^59.0.1", "globals": "^16.2.0", "husky": "^9.1.7", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.14", "stylelint": "^16.19.1", "stylelint-config-css-modules": "^4.4.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.8.0", "stylelint-prettier": "^5.0.3", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0", "wxt": "^0.20.6", "zod": "^3.25.34"}, "engines": {"node": ">=18.0.0", "pnpm": ">=9.0.0"}, "packageManager": "pnpm@9.15.4"}